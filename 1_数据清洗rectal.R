rm(list = ls())
setwd('/Users/<USER>/Desktop/rectal_cancer_MRI') # 请修改为您的工作目录
library(readxl)
library(dplyr)
library(writexl) # 添加writexl包用于输出xlsx文件

# 读取数据
file_path <- "CR-nearCR-c2.xlsx"  # 请替换为您的Excel文件路径
sheet_name <- "Sheet1"  # 请替换为您的工作表名称
data <- read_excel(file_path, sheet = sheet_name)

# 查看列名和数据结构
print("原始列名:")
print(colnames(data))
print("数据结构:")
str(data)
# 将列名中的空格和特殊字符替换为点
colnames(data) <- gsub("[ ,]", "_", colnames(data))
colnames(data) <- gsub("\\.\\.+", "_", colnames(data))
# 查看处理后的列名
print("处理后的列名:")
print(colnames(data))

# 1. 检查缺失值
summary(data)
# 2. 对指定列（如expression_level）均值填充
data$"CEA(ng/mL)"[is.na(data$"CEA(ng/mL)")] <- mean(data$"CEA(ng/mL)", na.rm = TRUE)
data$"CA-199(U/mL)"[is.na(data$"CA-199(U/mL)")] <- mean(data$"CA-199(U/mL)", na.rm = TRUE)
data$"CA-242(U/mL)"[is.na(data$"CA-242(U/mL)")] <- mean(data$"CA-242(U/mL)", na.rm = TRUE)
# 3. 验证
sum(is.na(data$"CEA(ng/mL)"))


library(dplyr)
data <- data %>% replace(is.na(.), "unknown")

# 重命名关键变量 - 根据实际列名动态调整
if("patient´ID_number" %in% colnames(data)) names(data)[names(data) == "patient´ID_number"] <- "Patient.ID"
if("gender(1-male_2-female)" %in% colnames(data)) names(data)[names(data) == "gender(1-male_2-female)"] <- "Sex"
if("age" %in% colnames(data)) names(data)[names(data) == "age"] <- "Age"
if("生存状态（1=复发转移；0=未复发转移）" %in% colnames(data)) names(data)[names(data) == "生存状态（1=复发转移；0=未复发转移）"] <- "Recurrence_status"
if("生存状态（1=死亡；0=生存）" %in% colnames(data)) names(data)[names(data) == "生存状态（1=死亡；0=生存）"] <- "Vital_status"
print(colnames(data))


# 查找可能的生存时间变量
# 精确匹配生存时间列（优先OS相关列）
os_time_pattern <- "^(OS[._-]?time|Overall[._-]?Survival|Survival[._-]?months)$"
surv_time_cols <- grep(os_time_pattern, colnames(data), ignore.case = TRUE, value = TRUE)

# 次选其他生存时间列（排除无关词）
other_time_pattern <- "^(PFS|LPFS|DMFS)[._-]?time$"
if (length(surv_time_cols) == 0) {
  surv_time_cols <- grep(other_time_pattern, colnames(data), ignore.case = TRUE, value = TRUE)
}

# 执行选择（增加列名白名单验证）
valid_os_cols <- c("OS.time", "OS_time", "OS-time", "Overall_Survival")
if (any(valid_os_cols %in% surv_time_cols)) {
  surv_time_var <- intersect(valid_os_cols, surv_time_cols)[1]
  data$Survival.months <- data[[surv_time_var]]
} else if (length(surv_time_cols) > 0) {
  surv_time_var <- surv_time_cols[1]
  data$Survival.months <- data[[surv_time_var]]
} else  {
  # 最终检查并提示
  possible_cols <- colnames(data)[sapply(data, is.numeric)]
  print(paste("未找到标准生存时间列，请手动指定。数值型列有:", 
              paste(possible_cols, collapse = ", ")))
  print("建议检查以下列是否存在: OS.time, OS_time, Survival.months")
}
table(data$Survival.months)



------------------------------------
# 处理性别变量
if("Sex" %in% colnames(data)) {
  data$Sex <- ifelse(!is.na(data$Sex) & data$Sex == 1, "Male", 
                    ifelse(!is.na(data$Sex) & data$Sex == 2, "Female", NA))
} else if("gender.1.male.2.female." %in% colnames(data)) {
  data$Sex <- ifelse(!is.na(data$gender.1.male.2.female.) & data$gender.1.male.2.female. == 1, "Male", 
                    ifelse(!is.na(data$gender.1.male.2.female.) & data$gender.1.male.2.female. == 2, "Female", NA))
}
##
#
#

# 处理肿瘤大小
data$Tumor_Size <- NA
# 检查肿瘤长径列名
tumor_size_cols <- grep("肿瘤长径", colnames(data), value = TRUE)
if(length(tumor_size_cols) > 0) {
  tumor_size_col <- tumor_size_cols[1]  # 使用第一个匹配的列
  print(paste("使用肿瘤长径列:", tumor_size_col))
  
  for(i in 1:nrow(data)){
    if(!is.na(data[[tumor_size_col]][i])){
      size_value = as.numeric(data[[tumor_size_col]][i])
      if(size_value <= 20){
        data$Tumor_Size[i] = "001-020"
      } else if(size_value <= 30){
        data$Tumor_Size[i] = "021-030"
      } else if(size_value <= 40){
        data$Tumor_Size[i] = "031-040"
      } else if(size_value <= 50){
        data$Tumor_Size[i] = "041-050"
      } else if(size_value <= 60){
        data$Tumor_Size[i] = "051-060"
      } else {
        data$Tumor_Size[i] = "060+"
      }
    } else {
      data$Tumor_Size[i] = "Unknown"
    }
  }
}
table(data$Tumor_Size)

# 处理治疗方式
# 放疗状态
if("Radiotherapy.Dose" %in% colnames(data)) {
  data$Radiotherapy <- ifelse(!is.na(data$Radiotherapy.Dose) & data$Radiotherapy.Dose != "", "Yes", "No")
} else {
  data$Radiotherapy <- "Unknown"
}


# 综合治疗方式
data$Treatment <- NA
for(i in 1:nrow(data)){
  if(data$Surgery[i] == "Yes" && data$Radiotherapy[i] == "Yes" && data$Chemotherapy[i] == "Yes"){
    data$Treatment[i] = "Surgery_RT_Chemo"
  } else if(data$Surgery[i] == "Yes" && data$Radiotherapy[i] == "Yes"){
    data$Treatment[i] = "Surgery_RT"
  } else if(data$Surgery[i] == "Yes" && data$Chemotherapy[i] == "Yes"){
    data$Treatment[i] = "Surgery_Chemo"
  } else if(data$Surgery[i] == "Yes"){
    data$Treatment[i] = "Surgery_alone"
  } else if(data$Radiotherapy[i] == "Yes" && data$Chemotherapy[i] == "Yes"){
    data$Treatment[i] = "RT_Chemo"
  } else {
    data$Treatment[i] = "Other"
  }
}
table(data$Treatment)
------------------------------------------------------------
  
  
  
  
# 计算放疗持续时间
if("Time-end_radiotherapy" %in% colnames(data) && "Time-start_radiotherapy" %in% colnames(data)) {
  # 将时间转换为数值型
  data$`radiotherapy-during-time(days)` <- as.numeric(data$`Time-end_radiotherapy` - data$`Time-start_radiotherapy`)
  # 处理可能的负值或异常值
  data$`radiotherapy-during-time(days)`[data$`radiotherapy-during-time(days)` < 0] <- NA
  print("已添加放疗持续时间列: radiotherapy-during-time(days)")
  print(summary(data$`radiotherapy-during-time(days)`))
} else {
  print("未找到放疗开始或结束时间列，无法计算放疗持续时间")
}

# 计算放疗结束到手术的时间间隔
if("Surgery_time" %in% colnames(data) && "Time-end_radiotherapy" %in% colnames(data)) {
  # 将时间转换为数值型
  data$`post-radio-pre-surg(days)` <- as.numeric(data$`Surgery_time` - data$`Time-end_radiotherapy`)
  # 处理可能的负值或异常值
  data$`post-radio-pre-surg(days)`[data$`post-radio-pre-surg(days)` < 0] <- NA
  print("已添加放疗结束到手术时间间隔列: post-radio-pre-surg(days)")
  print(summary(data$`post-radio-pre-surg(days)`))
} else {
  print("未找到手术时间或放疗结束时间列，无法计算放疗结束到手术的时间间隔")
}

# 处理生存状态
# 确认生存时间变量,创建新列 surv_time_var，自动选择有效生存时间数据
if ("Survival.months" %in% colnames(data) && !all(is.na(data$Survival.months))) {
  data$surv_time_var <- data$Survival.months  # 创建新列并赋值[2](@ref)[3](@ref)
} else if ("OS-time" %in% colnames(data) && !all(is.na(data$"OS-time"))) {
  data$surv_time_var <- data$"OS-time"  # 创建新列并赋值[2](@ref)[3](@ref)
} else {
  data$surv_time_var <- NA  # 无有效列时填充NA
}

# 只有在存在生存时间变量的情况下才处理生存状态
# ===== 1. 生存时间变量创建 =====
# 处理特殊列名并创建新列
if ("Survival.months" %in% colnames(data) && 
    !all(is.na(data$Survival.months))) {
  data$surv_time_var <- data$Survival.months
} else if ("OS.time" %in% colnames(data) && 
           !all(is.na(data$OS.time))) {
  data$surv_time_var <- data$OS.time
} else if ("OS-time" %in% colnames(data) && 
           !all(is.na(data$`OS-time`))) {  # 特殊字符处理
  data$surv_time_var <- data$`OS-time`
} else {
  data$surv_time_var <- NA_real_  # 保持数值类型
  warning("未找到有效生存时间列")
}

# ===== 2. 生存状态计算 =====
if ("surv_time_var" %in% colnames(data) && 
    !all(is.na(data$surv_time_var)) &&
    "Vital_status" %in% colnames(data)) {
  
  cat("计算生存状态...\n")
  
  # 1年生存状态（事件发生=1）
  data$One_year_status <- ifelse(
    data$surv_time_var < 12 & data$Vital_status == 1, 1, 0
  )
  
  # 3年生存状态
  data$Three_year_status <- ifelse(
    data$surv_time_var < 36 & data$Vital_status == 1, 1, 0
  )
  
  # 5年生存状态
  data$Five_year_status <- ifelse(
    data$surv_time_var < 60 & data$Vital_status == 1, 1, 0
  )
  
} else {
  warning("缺少生存时间或状态列，跳过计算")
  # 初始化空列
  status_cols <- c("One_year_status", "Three_year_status", "Five_year_status")
  data[status_cols] <- NA_real_
}

# 验证生存状态计算结果
cat("\n===== 生存状态统计摘要 =====\n")
status_cols <- c("One_year_status", "Three_year_status", "Five_year_status")
summary(data[status_cols])

cat("\n===== 各时间点事件发生率 =====\n")
sapply(status_cols, function(col) {
  prop.table(table(data[[col]])) * 100
})



# 动态构建可用变量列表
# 首先保留所有原始变量
original_vars <- colnames(data)

# 排除明显无用的变量（如全NA或常量）
exclude_vars <- c()
for(var in original_vars) {
  # 检查变量是否全为NA
  if(all(is.na(data[[var]]))) {
    exclude_vars <- c(exclude_vars, var)
    print(paste("变量", var, "全为NA，将被排除"))
    next
  }
  
  # 检查变量是否为常量（只有一个唯一值）
  if(length(unique(data[[var]])) == 1) {
    exclude_vars <- c(exclude_vars, var)
    print(paste("变量", var, "为常量，将被排除"))
    next
  }
}

# 获取所有有用的变量
useful_vars <- setdiff(original_vars, exclude_vars)
print("有用变量数量:")
print(length(useful_vars))

# 确保关键变量被包含
key_vars <- c("Patient.ID","Age","Sex","height(m)","tumor_differentiation",
              "CEA(ng/mL)","CA-199(U/mL)","CA-242(U/mL)","肿瘤T分期-初诊",
              "肿瘤N分期-初诊","侧方淋巴结-初诊","分期-初诊","腹膜返折-初诊","肿瘤距肛门距离(cm)-初诊",
              "EMVI(1有，2无)-初诊","环周切缘(CRM，1阳性，2阴性）-初诊","肛提肌侵犯，1侵犯，2无-初诊",
              "肿瘤长径（MRI）-初诊","肿瘤厚度-初诊","肠道占比-初诊","GTV(㎤)","Radiotherapy_Dose",
              "Concurrent_chemotherapy","Adjuvant_chemotherapy","AC-times",
              "肿瘤T分期-放化疗后","肿瘤N分期-放化疗后","侧方淋巴结-放化疗后",
              "EMVI(1有，2无)-放化疗后","环周切缘(CRM）-放化疗后","Sur-T","Sur-N","AJCC","TRG（AJCC）","Efficacy_evaluation","CCR(1-yes_0-no)",
              "pCR（1-yes，0-no）","Postoperative_chemotherapy_times","Postoperative_chemotherapy","Local_recurrence",
              "Metastasis_location","Recurrence_status","PFS-time","LPFS-status","LPFS-time",
              "DMFS-status","DMFS-time","Vital_status","OS-time","cCR_ncCR","post-radio-pre-surg(days)","radiotherapy-during-time(days)",
              "One_year_status","Three_year_status","Five_year_status")

key_vars <- key_vars[key_vars %in% colnames(data)]

# 合并所有要保留的变量
all_vars_to_keep <- unique(c(key_vars, useful_vars))
print("最终保留变量数量:")
print(length(all_vars_to_keep))
print("最终保留变量:")
print(all_vars_to_keep)

# 筛选变量
new_data <- data[all_vars_to_keep]

# 确定哪些变量应该是分类变量
# 检查每个变量的类型和唯一值数量
var_info <- data.frame(
  Variable = character(),
  Type = character(),
  UniqueValues = numeric(),
  stringsAsFactors = FALSE
)

for(var in colnames(new_data)) {
  var_type <- class(new_data[[var]])
  unique_values <- length(unique(na.omit(new_data[[var]])))
  var_info <- rbind(var_info, data.frame(
    Variable = var,
    Type = var_type[1],
    UniqueValues = unique_values,
    stringsAsFactors = FALSE
  ))
}

print("变量信息:")
print(var_info)

# 自动识别分类变量
# 1. 字符型变量
# 2. 唯一值少于10的数值变量
# 参数设置
MAX_UNIQUE_CAT <- 10      # 分类变量最大唯一值数
TEXT_LENGTH_THRESHOLD <- 20 # 文本变量长度阈值

cat_vars <- c()

for(i in 1:nrow(var_info)) {
  var <- var_info$Variable[i]
  col_data <- data[[var]]
  
  # 步骤1: 字符型变量数值转换检测
  if(var_info$Type[i] == "character") {
    num_convert <- suppressWarnings(as.numeric(col_data))
    convert_success_ratio <- sum(!is.na(num_convert)) / length(col_data)
    
    # 可转换为数值型的字符变量
    if(convert_success_ratio > 0.95) {
      data[[var]] <- num_convert  # 自动转换类型
      var_info$Type[i] <- "numeric"
      var_info$UniqueValues[i] <- length(unique(na.omit(num_convert)))
      message("🔄 转换数值型字符变量: ", var, " (成功率", 
              round(convert_success_ratio*100,1), "%)")
    }
  }
  
  # 步骤2: 分类变量判定逻辑
  current_type <- var_info$Type[i]
  unique_vals <- var_info$UniqueValues[i]
  
  # 字符型变量判定（排除长文本）
  if(current_type == "character") {
    avg_length <- mean(nchar(as.character(col_data)), na.rm = TRUE)
    if(unique_vals <= MAX_UNIQUE_CAT && avg_length < TEXT_LENGTH_THRESHOLD) {
      cat_vars <- c(cat_vars, var)
    }
  }
  # 数值型变量判定
  else if(current_type %in% c("numeric", "integer")) {
    if(unique_vals <= MAX_UNIQUE_CAT) {
      # 添加错误处理，确保数据是数值型
      tryCatch({
        if(all(col_data %% 1 == 0, na.rm = TRUE)) {
          cat_vars <- c(cat_vars, var)
        }
      }, error = function(e) {
        message("警告: 变量 ", var, " 包含非数值型数据，跳过整数检查")
      })
    }
  }
  # 因子型直接纳入
  else if(current_type == "factor") {
    cat_vars <- c(cat_vars, var)
  }
}

# 结果输出
cat("\n=== 最终分类变量列表 ===\n")
print(cat_vars)

# 手动添加已知的分类变量
additional_cat_vars <- c("Sex", "肿瘤T分期-放化疗后", "Recurrence_status", 
                         "One_year_status", "Three_year_status", "Five_year_status")
cat_vars <- unique(c(cat_vars, additional_cat_vars[additional_cat_vars %in% colnames(new_data)]))

print("识别的分类变量:")
print(cat_vars)

# 将分类变量转换为因子
for(var in cat_vars) {
  if(var %in% colnames(new_data)) {
    new_data[[var]] <- as.factor(new_data[[var]])
  }
}

# 保存数据框为RData格式
save(new_data, file = "rectal_cancer_MRI_cleaned.Rdata")

# 保存数据框为Excel格式
# 首先将因子转换为字符串，以便在Excel中正确显示
new_data_excel <- new_data
for(var in cat_vars) {
  if(var %in% colnames(new_data_excel)) {
    new_data_excel[[var]] <- as.character(new_data_excel[[var]])
  }
}
write_xlsx(new_data_excel, "rectal_cancer_MRI_cleaned.xlsx")
print("数据已保存为Excel格式：rectal_cancer_MRI_cleaned.xlsx")

# 查看因子水平
lever <- function(x) {
  if(is.factor(x)) {
    return(levels(x))
  } else {
    return("Not a factor")
  }
}
factor_levels <- lapply(new_data[cat_vars[cat_vars %in% colnames(new_data)]], lever)
print("因子水平:")
print(factor_levels)

# 显示变量表格
tables_list <- lapply(cat_vars[cat_vars %in% colnames(new_data)], function(var) {
  table(new_data[[var]], useNA = "ifany")
})
names(tables_list) <- cat_vars[cat_vars %in% colnames(new_data)]
print("分类变量频数表:")
print(tables_list)

# 保存最终数据框为RData格式
str(new_data)
save(new_data, file = "rectal_cancer_data_cleaned.Rdata")

# 保存最终数据框为Excel格式
write_xlsx(new_data_excel, "rectal_cancer_data_cleaned.xlsx")
print("最终数据已保存为Excel格式：rectal_cancer_data_cleaned.xlsx")
 
