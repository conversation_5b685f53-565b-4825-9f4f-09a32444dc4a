# 测试空白项处理功能
library(readxl)
library(dplyr)
library(writexl)

# 设置工作目录
setwd('/Users/<USER>/Desktop/rectal_cancer_MRI')

# 读取数据
cat("正在读取数据...\n")
data <- read_excel("CR-nearCR1.xlsx", sheet = "Sheet1")

cat("数据维度:", dim(data), "\n")
cat("前5行数据:\n")
print(head(data, 5))

# 定义处理空白项的函数
fill_missing_values <- function(data, fill_value = "unknown") {
  cat("开始搜索和处理空白项...\n")
  
  # 记录原始缺失情况
  original_missing <- list()
  
  for(col in colnames(data)) {
    # 统计各种类型的空白项
    na_count <- sum(is.na(data[[col]]))

    # 安全地处理字符串比较，避免日期类型错误
    empty_string_count <- 0
    whitespace_count <- 0
    null_like_count <- 0

    # 只对字符型数据进行字符串操作
    if(is.character(data[[col]])) {
      empty_string_count <- sum(data[[col]] == "", na.rm = TRUE)
      whitespace_count <- sum(grepl("^\\s*$", data[[col]]), na.rm = TRUE) - empty_string_count
      null_like_count <- sum(data[[col]] %in% c("NULL", "null", "无", "缺失", "未知"), na.rm = TRUE)
    }
    
    total_missing <- na_count + empty_string_count + whitespace_count + null_like_count
    
    if(total_missing > 0) {
      original_missing[[col]] <- list(
        na = na_count,
        empty_string = empty_string_count,
        whitespace = whitespace_count,
        null_like = null_like_count,
        total = total_missing,
        percentage = round(total_missing / nrow(data) * 100, 2)
      )
    }
  }
  
  # 打印缺失值报告
  cat("\n=== 缺失值检测报告 ===\n")
  if(length(original_missing) == 0) {
    cat("未发现任何缺失值或空白项。\n")
  } else {
    for(col in names(original_missing)) {
      missing_info <- original_missing[[col]]
      cat(sprintf("变量 '%s': 总缺失 %d 个 (%.2f%%)\n", 
                  col, missing_info$total, missing_info$percentage))
      cat(sprintf("  - NA值: %d\n", missing_info$na))
      cat(sprintf("  - 空字符串: %d\n", missing_info$empty_string))
      cat(sprintf("  - 空白字符串: %d\n", missing_info$whitespace))
      cat(sprintf("  - 类NULL值: %d\n", missing_info$null_like))
      cat("\n")
    }
  }
  
  # 处理空白项
  cat("开始填充空白项...\n")
  filled_count <- 0
  
  for(col in colnames(data)) {
    # 处理字符型变量
    if(is.character(data[[col]]) || is.factor(data[[col]])) {
      # 转换为字符型以便处理
      if(is.factor(data[[col]])) {
        data[[col]] <- as.character(data[[col]])
      }
      
      # 标记需要填充的位置
      to_fill <- is.na(data[[col]]) | 
                 data[[col]] == "" | 
                 grepl("^\\s*$", data[[col]]) |
                 data[[col]] %in% c("NULL", "null", "无", "缺失", "未知")
      
      # 填充
      data[[col]][to_fill] <- fill_value
      filled_count <- filled_count + sum(to_fill, na.rm = TRUE)
      
    } else if(is.numeric(data[[col]]) || is.integer(data[[col]])) {
      # 对于数值型变量，只处理NA值
      na_positions <- is.na(data[[col]])
      if(sum(na_positions) > 0) {
        # 对于数值变量，转为字符型后填充
        data[[col]] <- as.character(data[[col]])
        data[[col]][na_positions] <- fill_value
        filled_count <- filled_count + sum(na_positions)
      }
    }
  }
  
  cat(sprintf("总共填充了 %d 个空白项。\n", filled_count))
  
  # 返回处理后的数据和报告
  return(list(
    data = data,
    missing_report = original_missing,
    filled_count = filled_count
  ))
}

# 应用空白项处理
cat("正在处理数据中的空白项...\n")
result <- fill_missing_values(data, "unknown")
data_processed <- result$data
missing_report <- result$missing_report

# 保存缺失值报告
if(length(missing_report) > 0) {
  # 创建缺失值报告数据框
  missing_df <- data.frame(
    Variable = character(),
    Total_Missing = numeric(),
    Percentage = numeric(),
    NA_Count = numeric(),
    Empty_String = numeric(),
    Whitespace = numeric(),
    Null_Like = numeric(),
    stringsAsFactors = FALSE
  )
  
  for(col in names(missing_report)) {
    missing_df <- rbind(missing_df, data.frame(
      Variable = col,
      Total_Missing = missing_report[[col]]$total,
      Percentage = missing_report[[col]]$percentage,
      NA_Count = missing_report[[col]]$na,
      Empty_String = missing_report[[col]]$empty_string,
      Whitespace = missing_report[[col]]$whitespace,
      Null_Like = missing_report[[col]]$null_like,
      stringsAsFactors = FALSE
    ))
  }
  
  # 按缺失百分比排序
  missing_df <- missing_df[order(missing_df$Percentage, decreasing = TRUE), ]
  
  # 保存报告
  write_xlsx(missing_df, "missing_values_report.xlsx")
  cat("缺失值报告已保存为: missing_values_report.xlsx\n")
  
  # 打印前10个缺失最多的变量
  cat("\n=== 缺失值最多的前10个变量 ===\n")
  print(head(missing_df, 10))
} else {
  cat("没有发现缺失值，无需生成报告。\n")
}

# 保存处理后的数据
write_xlsx(data_processed, "data_with_filled_missing.xlsx")
cat("处理后的数据已保存为: data_with_filled_missing.xlsx\n")

cat("空白项处理完成！\n")
