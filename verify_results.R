# 验证空白项处理结果
library(readxl)

# 读取处理后的数据
data <- read_excel('data_with_filled_missing.xlsx')

cat('处理后数据维度:', dim(data), '\n')

# 检查unknown填充情况
unknown_counts <- sapply(data, function(x) sum(x == 'unknown', na.rm=TRUE))
unknown_vars <- unknown_counts[unknown_counts > 0]

cat('\n=== 已填充"unknown"的变量 ===\n')
for(var in names(unknown_vars)) {
  cat(sprintf('%s: %d个unknown\n', var, unknown_vars[var]))
}

cat('\n总共填充了', sum(unknown_counts), '个unknown值\n')

# 检查是否还有NA值
na_counts <- sapply(data, function(x) sum(is.na(x)))
remaining_na <- na_counts[na_counts > 0]

if(length(remaining_na) > 0) {
  cat('\n=== 仍有NA值的变量 ===\n')
  for(var in names(remaining_na)) {
    cat(sprintf('%s: %d个NA\n', var, remaining_na[var]))
  }
} else {
  cat('\n✓ 所有NA值已被处理\n')
}

# 检查是否还有空字符串
empty_counts <- sapply(data, function(x) {
  if(is.character(x)) {
    sum(x == "", na.rm=TRUE)
  } else {
    0
  }
})
remaining_empty <- empty_counts[empty_counts > 0]

if(length(remaining_empty) > 0) {
  cat('\n=== 仍有空字符串的变量 ===\n')
  for(var in names(remaining_empty)) {
    cat(sprintf('%s: %d个空字符串\n', var, remaining_empty[var]))
  }
} else {
  cat('\n✓ 所有空字符串已被处理\n')
}

cat('\n空白项处理验证完成！\n')
